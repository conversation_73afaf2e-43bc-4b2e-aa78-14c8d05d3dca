# bff-template 项目需求简介

## 项目名称：中台项目模板

## 项目背景

1. 很多时候新项目开始时，前端开发进度与后端开发进度无法保持一致，前端开发速度较快，后端开发进度未及时跟上，导致前端开发完后没有接口可以联调，影响前端开发完整度，所以需要一个可以模拟的接口
2. 现在很多项目都是以微服务的形式部署的，尤其是登录鉴权服务与业务服务在不同的服务上，但是前端开发某个业务服务时，因为跨服务与跨域的原因会导致开发调试很困难，所以需要一个本地的中台去转发登录，与转发其他服务的请求

## 项目技术栈

- Nodejs
- NestJS
- Typescript
- ESlint
- Prettier
- Husky
- Commitizen
- Commitlint
- Standard-version
- Github Actions

## 实现细节说明

1. 使用通用的 NestJS 项目目录结构
2. 使用 NestJS 的 Module、Controller、Service、Interceptor、Guard、Filter 等功能
3. 使用 NestJS 的 HttpModule 来转发请求
4. 使用 NestJS 的 ConfigModule 来管理配置
5. 使用 NestJS 的 LoggerModule 来管理日志
6. 使用 NestJS 的 ExceptionFilter 来管理异常
7. 使用 NestJS 的 Interceptor 来管理请求与响应的拦截
8. 转发请求的 API 前缀需要使用变量去配置
9. 转发请求的 API 地址需要使用变量去配置
